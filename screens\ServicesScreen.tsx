"use client";
import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";

const ServicesScreen: React.FC = () => {
  const { t, isRTL } = useLanguage();

  const coreServices = [
    {
      titleKey: "services.tax.title",
      descriptionKey: "services.tax.description",
      itemsKeys: [
        "services.tax.items.corporate",
        "services.tax.items.vat",
        "services.tax.items.international",
        "services.tax.items.compliance",
      ],
      delay: "0.3s",
      anchor: "tax-advisory-anchor",
    },
    {
      titleKey: "services.company.title",
      descriptionKey: "services.company.description",
      itemsKeys: [
        "services.company.items.freezone",
        "services.company.items.mainland",
        "services.company.items.offshore",
        "services.company.items.licensing",
      ],
      delay: "0.4s",
      anchor: "company-formation-anchor",
    },
    {
      titleKey: "services.trust.title",
      descriptionKey: "services.trust.description",
      itemsKeys: [
        "services.trust.items.family",
        "services.trust.items.asset",
        "services.trust.items.succession",
        "services.trust.items.foundation",
      ],
      delay: "0.5s",
      anchor: "trust-establishment-anchor",
    },
  ];

  const workSteps = [
    {
      titleKey: "services.work.step1.title",
      descriptionKey: "services.work.step1.description",
      delay: "0.3s",
    },
    {
      titleKey: "services.work.step2.title",
      descriptionKey: "services.work.step2.description",
      delay: "0.4s",
    },
    {
      titleKey: "services.work.step3.title",
      descriptionKey: "services.work.step3.description",
      delay: "0.5s",
    },
  ];

  return (
    <div className="bg-gradient-to-br from-slate-50 to-white min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#e9f5ec] to-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`max-w-4xl mx-auto ${isRTL ? "text-right" : "text-center"}`}
          >
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-[#1d4930]">
              {t("services.hero.title")}
            </h1>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              {t("services.hero.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Services Cards */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8 mb-16">
            {coreServices.map((service) => (
              <div
                key={service.titleKey}
                id={service.anchor}
                className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:border-green-700/40 hover:shadow-xl transition-all duration-300 anim-target anim-fade-up is-visible"
                style={{ transitionDelay: service.delay }}
              >
                <h3 className="text-xl font-bold text-[#1d4930] mb-4">
                  {t(service.titleKey)}
                </h3>
                <p className="text-gray-600 mb-4">
                  {t(service.descriptionKey)}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1 pl-2">
                  {service.itemsKeys.map((itemKey) => (
                    <li key={itemKey}>{t(itemKey)}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* How We Work Section */}
          <div className="bg-gradient-to-br from-[#1d4930] to-[#2d5940] rounded-3xl p-10 mb-20 border border-green-700/30 shadow-2xl">
            <div className={`${isRTL ? "text-right" : "text-center"} mb-12`}>
              <h2 className="text-3xl font-extrabold text-white mb-4 tracking-tight drop-shadow">
                {t("services.work.title")}
              </h2>
              <p className="text-gray-100 max-w-2xl mx-auto text-lg">
                {t("services.work.subtitle")}
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-10">
              {workSteps.map((step, idx) => (
                <div
                  key={step.titleKey}
                  className="bg-white rounded-2xl p-8 shadow-xl border border-green-700/10 hover:shadow-2xl hover:border-green-700/40 transition-all duration-300 flex flex-col items-center anim-target anim-fade-up is-visible"
                  style={{ transitionDelay: step.delay }}
                >
                  <div className="mb-4">
                    <span className="inline-block bg-green-100 text-green-700 rounded-full p-3 shadow">
                      {idx === 0 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <circle cx="14" cy="14" r="12" />
                          <path d="M14 8v8l6 4" />
                        </svg>
                      )}
                      {idx === 1 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <rect x="6" y="6" width="16" height="16" rx="4" />
                          <path d="M10 14h8" />
                        </svg>
                      )}
                      {idx === 2 && (
                        <svg
                          width="28"
                          height="28"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="inline"
                        >
                          <path d="M14 4v20" />
                          <path d="M4 14h20" />
                        </svg>
                      )}
                    </span>
                  </div>
                  <h3 className="font-bold text-lg text-[#1d4930] mb-2 text-center">
                    {t(step.titleKey)}
                  </h3>
                  <p className="text-gray-600 text-center">
                    {t(step.descriptionKey)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServicesScreen;
