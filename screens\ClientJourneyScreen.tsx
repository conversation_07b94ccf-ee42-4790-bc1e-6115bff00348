"use client";
import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";

interface ClientJourneyScreenProps {
  // Add any props you need here
}

const ClientJourneyScreen: React.FC<ClientJourneyScreenProps> = () => {
  const { t, isRTL } = useLanguage();
  const journeySteps = [
    {
      step: 1,
      title: t("journey.step1.title"),
      description: t("journey.step1.description"),
      icon: "🔍",
    },
    {
      step: 2,
      title: t("journey.step2.title"),
      description: t("journey.step2.description"),
      icon: "📋",
    },
    {
      step: 3,
      title: t("journey.step3.title"),
      description: t("journey.step3.description"),
      icon: "⚡",
    },
    {
      step: 4,
      title: t("journey.step4.title"),
      description: t("journey.step4.description"),
      icon: "🤝",
    },
  ];

  return (
    <div
      className={`bg-gradient-to-br from-slate-50 to-white min-h-screen ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Hero Section */}
      <div className="bg-[#e9f5ec] to-white text-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`max-w-4xl mx-auto ${isRTL ? "text-right" : "text-center"}`}
          >
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-primary">
              {t("journey.hero.title")}
            </h1>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              {t("journey.hero.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Process Steps */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {journeySteps.map((item, index) => (
                <div key={index} className="relative group">
                  {/* Step Card */}
                  <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#1d4930]/20 transform hover:-translate-y-2">
                    {/* Step Number */}
                    <div className="absolute -top-4 left-8">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#1d4930] to-[#2d5940] rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                        {item.step}
                      </div>
                    </div>

                    {/* Icon */}
                    <div className="mt-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#1d4930]/10 to-[#2d5940]/10 rounded-xl flex items-center justify-center mx-auto">
                        <span className="text-3xl">{item.icon}</span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {item.description}
                      </p>
                    </div>

                    {/* Connecting Arrow (except for last item) */}
                    {index < journeySteps.length - 1 && (
                      <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                        <div className="w-8 h-0.5 bg-gradient-to-r from-[#1d4930] to-[#2d5940]"></div>
                        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1">
                          <div className="w-0 h-0 border-l-4 border-l-[#1d4930] border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Benefits Section */}
            <div className="bg-gradient-to-br from-[#1d4930]/5 to-[#2d5940]/5 rounded-2xl p-8 mb-16 border border-[#1d4930]/10">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {t("journey.benefits.title")}
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  {t("journey.benefits.subtitle")}
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#1d4930] rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-xl">⚡</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {t("journey.benefits.fast")}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {t("journey.benefits.fastDesc")}
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-[#1d4930] rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-xl">🎯</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {t("journey.benefits.personalized")}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {t("journey.benefits.personalizedDesc")}
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-[#1d4930] rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-xl">🔒</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {t("journey.benefits.secure")}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {t("journey.benefits.secureDesc")}
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center bg-white rounded-2xl shadow-xl p-12 border border-gray-100">
              <div className="max-w-2xl mx-auto">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">
                  {t("journey.cta.title")}
                </h2>
                <p className="text-gray-600 mb-8 text-lg">
                  {t("journey.cta.subtitle")}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="bg-gradient-to-r from-[#1d4930] to-[#2d5940] text-white px-8 py-4 rounded-xl hover:from-[#2d5940] hover:to-[#1d4930] transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    {t("journey.cta.start")}
                    <span className="ml-2">→</span>
                  </button>
                  <button className="border-2 border-[#1d4930] text-[#1d4930] px-8 py-4 rounded-xl hover:bg-[#1d4930] hover:text-white transition-all duration-300 font-semibold text-lg">
                    {t("journey.cta.schedule")}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientJourneyScreen;
