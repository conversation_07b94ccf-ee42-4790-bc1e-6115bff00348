"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

const PdfViewer = dynamic(() => import("@/components/PdfViewer"), {
  ssr: false, // Prevents hydration issues
});
export default function PdfViewerPage() {
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  if (!mounted) return <div>Loading PDF Viewer...</div>;
  return (
    <div>
      <div className="p-4">
        <PdfViewer fileUrl="/pdf/Reading a Factsheet.pdf" />
      </div>
    </div>
  );
}
