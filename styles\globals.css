@tailwind base;
@tailwind components;
@tailwind utilities;

/* Floating animation for sticky image button */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-12px); }
}
.animate-float {
  animation: float 2s ease-in-out infinite;
}

/* Fade-in animation for chat box */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: none; }
}
.animate-fadeIn {
  animation: fadeIn 0.3s ease;
}
