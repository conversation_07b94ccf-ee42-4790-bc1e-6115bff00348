"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@heroui/drawer";
import { Input } from "@heroui/input";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/popover";
import { useState, useMemo, useEffect } from "react";

// Example user list with details
const usersData = [
  { name: "<PERSON>", email: "<EMAIL>", role: "Ad<PERSON>" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Manager" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Ad<PERSON>" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Moderator" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Manager" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Manager" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "Brandon", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Admin" },
  { name: "Caleb", email: "<EMAIL>", role: "User" },
  { name: "Cameron", email: "<EMAIL>", role: "User" },
  { name: "Caroline", email: "<EMAIL>", role: "Manager" },
  { name: "Carter", email: "<EMAIL>", role: "User" },
  { name: "Charlotte", email: "<EMAIL>", role: "Admin" },
  { name: "Chloe", email: "<EMAIL>", role: "User" },
  { name: "Christopher", email: "<EMAIL>", role: "Moderator" },
  { name: "Claire", email: "<EMAIL>", role: "User" },
  { name: "Daniel", email: "<EMAIL>", role: "Manager" },
  { name: "David", email: "<EMAIL>", role: "User" },
  { name: "Dominic", email: "<EMAIL>", role: "Admin" },
  { name: "Dylan", email: "<EMAIL>", role: "User" },
  { name: "Eleanor", email: "<EMAIL>", role: "User" },
  { name: "Elijah", email: "<EMAIL>", role: "Manager" },
  { name: "Elizabeth", email: "<EMAIL>", role: "User" },
  { name: "Ella", email: "<EMAIL>", role: "Admin" },
  { name: "Ethan", email: "<EMAIL>", role: "User" },
  { name: "Evelyn", email: "<EMAIL>", role: "Moderator" },
  { name: "Gabriel", email: "<EMAIL>", role: "User" },
  { name: "Grace", email: "<EMAIL>", role: "Admin" },
  { name: "Hannah", email: "<EMAIL>", role: "User" },
  { name: "Harper", email: "<EMAIL>", role: "Manager" },
  { name: "Henry", email: "<EMAIL>", role: "User" },
  { name: "Isabella", email: "<EMAIL>", role: "Admin" },
  { name: "Isla", email: "<EMAIL>", role: "User" },
  { name: "Jack", email: "<EMAIL>", role: "User" },
  { name: "Jackson", email: "<EMAIL>", role: "Manager" },
  { name: "Jacob", email: "<EMAIL>", role: "User" },
  { name: "James", email: "<EMAIL>", role: "Admin" },
  { name: "Jasmine", email: "<EMAIL>", role: "User" },
  { name: "Jayden", email: "<EMAIL>", role: "Moderator" },
  { name: "John", email: "<EMAIL>", role: "User" },
  { name: "Joseph", email: "<EMAIL>", role: "Manager" },
];

export default function AppDrawer({ isOpen, onClose }) {
  const [search, setSearch] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => setIsMobile(window.innerWidth < 640);
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Filter & sort alphabetically
  const filteredUsers = useMemo(() => {
    return usersData
      .filter((user) => user.name.toLowerCase().includes(search.toLowerCase()))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [search]);

  // Group by first letter
  const groupedUsers = useMemo(() => {
    return filteredUsers.reduce((groups, user) => {
      const firstLetter = user.name.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(user);
      return groups;
    }, {});
  }, [filteredUsers]);

  return (
    <Drawer isOpen={isOpen} onOpenChange={onClose} placement="right" size="sm">
      <DrawerContent>
        {(close) => (
          <>
            <DrawerHeader className="flex flex-col gap-2">
              <span className="text-lg font-semibold">User List</span>
              <Input
                placeholder="Search users..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                clearable
                className="w-full"
              />
            </DrawerHeader>

            <DrawerBody>
              {Object.keys(groupedUsers).length > 0 ? (
                Object.keys(groupedUsers).map((letter) => (
                  <div key={letter} className="mb-4">
                    <h3 className="font-bold text-gray-700">{letter}</h3>
                    <ul className="ml-4 mt-1 space-y-1">
                      {groupedUsers[letter].map((user, index) => (
                        <li key={index}>
                          <Popover
                            placement={
                              isMobile ? "bottom-start" : "right-start"
                            }
                          >
                            <PopoverTrigger>
                              <div className="p-2 bg-gray-100 rounded hover:bg-gray-200 cursor-pointer">
                                {user.name}
                              </div>
                            </PopoverTrigger>
                            <PopoverContent className="p-4 max-w-[250px] sm:max-w-xs">
                              <div>
                                <h4 className="font-semibold">{user.name}</h4>
                                <p className="text-sm text-gray-600">
                                  Email: {user.email}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Role: {user.role}
                                </p>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))
              ) : (
                <p className="text-gray-400 text-sm">No users found</p>
              )}
            </DrawerBody>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
}
