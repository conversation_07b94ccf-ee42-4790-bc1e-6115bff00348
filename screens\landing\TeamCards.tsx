// components/TeamCards.tsx
"use client";
import React from "react";

const teamMembers = [
  {
    name: "GEORGE S K",
    role: "Partner",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "ADAM HUSBAND",
    role: "Director (Consultancy)",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "S KURUVILA",
    role: "Chief Operations Officer",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "ABDELRAHMAN ELHADI",
    role: "Senior Legal Advisor",
    image: "https://via.placeholder.com/400x500",
  },
];

export default function TeamCards() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 p-6 bg-gray-100">
      {teamMembers.map((member, index) => (
        <div
          key={index}
          className="relative group bg-white shadow-lg overflow-hidden rounded-lg"
        >
          {/* Image */}
          <img
            // src={member.image}
            src="/dubaiskyline.webp"
            alt={member.name}
            className="w-full h-[400px] object-cover grayscale-0 transition duration-500 group-hover:grayscale-0"
          />

          {/* Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition duration-500 flex flex-col justify-end p-4">
            <h2 className="text-white text-lg font-bold">{member.name}</h2>
            <p className="text-gray-200 text-sm mb-4">{member.role}</p>
            <button className="bg-yellow-500 text-black px-4 py-2 rounded hover:bg-yellow-400">
              Contact
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
