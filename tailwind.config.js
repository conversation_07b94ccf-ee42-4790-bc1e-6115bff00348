import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
    "./screens/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        primary: {
          DEFAULT: "#800000",
          50: "#f0f9f4", // very light minty green
          100: "#dcf2e4",
          200: "#bce5cd",
          300: "#8dd1ab",
          400: "#5bab84", // lighter than base
          500: "#800000", // base color
          600: "#183d29", // slightly darker
          700: "#133221",
          800: "#0e2619",
          900: "#0a1b12",
          950: "#05100b",
          foreground: "#ffffff",
        },

        secondary: {
          DEFAULT: "#c9a338",
          50: "#fefbf3",
          100: "#fdf6e3",
          200: "#faecc2",
          300: "#f6dd96",
          400: "#f0c968",
          500: "#eab543",
          600: "#c9a338",
          700: "#b8912f",
          800: "#96742b",
          900: "#7a5f28",
          950: "#453513",
          foreground: "#ffffff",
        },
      },
    },
  },
  darkMode: "class",
  plugins: [heroui()],
};

module.exports = config;
