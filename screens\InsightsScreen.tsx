"use client";
import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";

interface InsightsScreenProps {
  // Add any props you need here
}

interface InsightCard {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  date: string;
}

const InsightsScreen: React.FC<InsightsScreenProps> = () => {
  const { t, isRTL } = useLanguage();
  const insights: InsightCard[] = [
    {
      id: 1,
      title: t("insights.article1.title"),
      excerpt: t("insights.article1.description"),
      category: t("insights.categories.tax"),
      readTime: `5 ${t("common.minRead")}`,
      date: "2024-04-18",
    },
    {
      id: 2,
      title: t("insights.article2.title"),
      excerpt: t("insights.article2.description"),
      category: t("insights.categories.trust"),
      readTime: `7 ${t("common.minRead")}`,
      date: "2024-03-28",
    },
    {
      id: 3,
      title: t("insights.article3.title"),
      excerpt: t("insights.article3.description"),
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2024-02-10",
    },
    {
      id: 4,
      title: t("International Tax Compliance"),
      excerpt: t(
        "Navigate the complex world of international tax regulations and ensure full compliance."
      ),
      category: t("insights.categories.tax"),
      readTime: `8 ${t("common.minRead")}`,
      date: "2024-01-15",
    },
    {
      id: 5,
      title: t("Wealth Preservation Strategies"),
      excerpt: t(
        "Learn effective strategies to preserve and grow your wealth across different jurisdictions."
      ),
      category: t("insights.categories.investment"),
      readTime: `10 ${t("common.minRead")}`,
      date: "2023-12-20",
    },
    {
      id: 6,
      title: t("Dubai Free Zone Benefits"),
      excerpt: t(
        "Explore the advantages of setting up your business in Dubai's various free zones."
      ),
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2023-11-30",
    },
  ];

  const categories = [
    t("insights.categories.all"),
    t("insights.categories.tax"),
    t("insights.categories.trust"),
    t("insights.categories.business"),
    t("insights.categories.investment"),
  ];

  return (
    <div
      className={`bg-gradient-to-br from-slate-50 to-white min-h-screen ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Hero Section */}
      <div className="bg-[#e9f5ec] py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`max-w-4xl mx-auto ${isRTL ? "text-right" : "text-center"}`}
          >
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-[#1d4930]">
              {t("insights.hero.title")}
            </h1>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              {t("insights.hero.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category}
                  className="px-4 py-2 rounded-full border border-gray-300 bg-white hover:border-primary hover:text-primary hover:bg-primary/5 transition-colors text-gray-700"
                >
                  {category}
                </button>
              ))}
            </div>

            {/* Featured Insight */}
            <div className="bg-primary rounded-lg p-8 text-white mb-12 border border-primary/80">
              <div className="max-w-3xl">
                <span className="inline-block bg-white bg-opacity-90 text-primary px-3 py-1 rounded-full text-sm mb-4 font-medium">
                  Featured
                </span>
                <h2 className="text-3xl font-bold mb-4">{insights[0].title}</h2>
                <p className="text-lg mb-6 text-primary-50">
                  {insights[0].excerpt}
                </p>
                <div className="flex items-center gap-4 text-sm text-primary-100">
                  <span>{insights[0].category}</span>
                  <span>•</span>
                  <span>{insights[0].readTime}</span>
                  <span>•</span>
                  <span>{new Date(insights[0].date).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {/* Insights Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {insights.slice(1).map((insight) => (
                <article
                  key={insight.id}
                  className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden hover:border-gray-300 hover:shadow-sm transition-all"
                >
                  <div className="h-48 bg-gray-300"></div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                        {insight.category}
                      </span>
                      <span className="text-xs text-gray-600">
                        {insight.readTime}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold mb-3 hover:text-primary cursor-pointer text-gray-900">
                      {insight.title}
                    </h3>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {insight.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {new Date(insight.date).toLocaleDateString()}
                      </span>
                      <button className="text-primary hover:text-primary/80 font-medium text-sm">
                        {t("common.readMore")} →
                      </button>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div
              className={`bg-gray-100 rounded-lg p-8 mt-16 border border-gray-200 ${isRTL ? "text-right" : "text-center"}`}
            >
              <h2 className="text-2xl font-bold mb-4 text-gray-900">
                {t("insights.newsletter.title")}
              </h2>
              <p className="text-gray-700 mb-6">
                {t("insights.newsletter.subtitle")}
              </p>
              <div className="flex max-w-md mx-auto gap-4">
                <input
                  type="email"
                  placeholder={t("insights.newsletter.placeholder")}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-primary bg-white"
                />
                <button className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors shadow-sm">
                  {t("insights.newsletter.subscribe")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsScreen;
