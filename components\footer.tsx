"use client";
import { Link } from "@heroui/link";
import { siteConfig } from "@/config/site";
import { useLanguage } from "@/contexts/LanguageContext";
import { useState } from "react";
import Image from "next/image";

export const Footer = () => {
  const { t, isRTL } = useLanguage();
  const [chatOpen, setChatOpen] = useState(false);
  const [chatInput, setChatInput] = useState("");
  const [chatMessages, setChatMessages] = useState([
    { from: "bot", text: "Hi! How can I help you today?" },
  ]);

  const handleSend = () => {
    if (!chatInput.trim()) return;
    setChatMessages((msgs) => [
      ...msgs,
      { from: "user", text: chatInput },
      {
        from: "bot",
        text: `Thanks you for your message. An adviser will be with you shortly.`,
      }, // Simple echo response
    ]);
    setChatInput("");
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleSend();
  };
  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
                <Image
                  src="/pmw.png"
                  alt="Proactive Wealth Management Logo"
                  width={32}
                  height={32}
                  className="w-10 h-10 max-md:w-5 max-md:h-5"
                  priority
                />
              </div>
              <div className="flex flex-col">
                <p className="font-bold text-white text-sm leading-tight">
                  Proactive
                </p>
                <p className="font-bold text-white text-sm leading-tight">
                  Wealth Management
                </p>
              </div>
            </div>
            <p className="text-slate-300 mb-6 max-w-md leading-relaxed">
              {t("footer.description")}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-white mb-4">
              {t("footer.quickLinks")}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.about")}
                </Link>
              </li>
              <li>
                <Link
                  href="/services"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.services")}
                </Link>
              </li>
              <li>
                <Link
                  href="/insights"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.insights")}
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.contact")}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-white mb-4">
              {t("footer.contact")}
            </h3>
            <div className="space-y-2 text-slate-300">
              <p>{siteConfig.contact.address}</p>
              <p>Phone: {siteConfig.contact.phone}</p>
              <p>Email: {siteConfig.contact.email}</p>
            </div>

            {/* Social Media Icons */}
            <div className="mt-6">
              <h4 className="font-semibold text-white mb-3">
                {t("footer.followUs")}
              </h4>
              <div
                className={`flex ${isRTL ? "space-x-reverse" : ""} space-x-4`}
              >
                <Link
                  href="https://linkedin.com/company/pwmuae"
                  className="text-slate-300 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </Link>

                <Link
                  href="https://twitter.com/pwmuae"
                  className="text-slate-300 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                  </svg>
                </Link>

                <Link
                  href="https://facebook.com/pwmuae"
                  className="text-slate-300 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </Link>

                <Link
                  href="https://instagram.com/pwmuae"
                  className="text-slate-300 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </Link>

                <Link
                  href="https://youtube.com/@pwmuae"
                  className="text-slate-300 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-800 mt-12 pt-8 text-center">
          <div className="text-center w-full flex flex-col items-center justify-center">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <p className="text-slate-400 text-sm">{t("footer.rights")}</p>
            </div>
            <div className="flex flex-row items-center gap-6 w-full text-center justify-center pt-2">
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                {t("footer.privacy")}
              </Link>
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                {t("footer.disclaimer")}
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Widget Placeholder - Left Bottom */}
      <div className="fixed bottom-6 left-6 z-50">
        <button
          className="bg-secondary text-white p-4 max-md:p-2 rounded-full shadow-lg hover:bg-secondary-300 transition-colors"
          onClick={() => setChatOpen((open) => !open)}
        >
          <span className="text-lg">💬</span>
        </button>

        {chatOpen && (
          <div
            className="absolute bottom-20 max-md:bottom-12 left-0 w-[92vw] sm:w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col overflow-hidden animate-fadeIn transition-all duration-300"
            style={{ maxWidth: "95vw" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-2 font-semibold bg-gradient-to-r from-primary-900 to-primary-700 text-white shadow rounded-t-2xl">
              <span className="tracking-wide">Ask PWM</span>
              <button
                className="ml-2 p-1 rounded-full hover:bg-white/10 text-white text-xl focus:outline-none focus:ring-2 focus:ring-white/50 transition"
                onClick={() => setChatOpen(false)}
                aria-label="Close chat"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 6L14 14M14 6L6 14"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            {/* Messages */}
            <div
              className="flex-1 p-4 space-y-2 overflow-y-auto text-sm bg-gray-50"
              style={{ maxHeight: "60vh", minHeight: 120 }}
            >
              {chatMessages.map((msg, i) => (
                <div
                  key={i}
                  className={
                    msg.from === "user"
                      ? "flex justify-end"
                      : "flex justify-start"
                  }
                >
                  <span
                    className={`px-4 py-2 rounded-2xl shadow ${
                      msg.from === "user"
                        ? "bg-primary-100 text-primary-900"
                        : "bg-white border border-gray-200 text-gray-900"
                    } inline-block max-w-full sm:max-w-[80%] break-words whitespace-pre-line`}
                    style={{
                      wordBreak: "break-word",
                      overflowWrap: "break-word",
                    }}
                  >
                    {msg.text}
                  </span>
                </div>
              ))}
            </div>

            {/* Input & Send */}
            <div className="flex border-t border-gray-200 bg-white p-2 gap-2 w-full">
              <input
                className="flex-1 px-3 py-2 rounded-xl border border-gray-200 focus:ring-2 focus:ring-primary-400 outline-none text-gray-900 bg-gray-50 shadow-sm"
                type="text"
                placeholder="Type your message..."
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleInputKeyDown}
              />
              <button
                className="shrink-0 bg-gradient-to-r from-primary-900 to-primary-700 text-white px-4 py-2 rounded-xl font-semibold shadow hover:from-primary-700 hover:to-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 transition-all"
                onClick={handleSend}
              >
                Send
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Image Widget - Right Bottom */}
    </footer>
  );
};
