"use client";
import React, { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";

interface ContactScreenProps {
  // Add any props you need here
}

interface FormData {
  name: string;
  email: string;
  company: string;
  phone: string;
  subject: string;
  message: string;
}

const ContactScreen: React.FC<ContactScreenProps> = () => {
  const { t, isRTL } = useLanguage();
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    company: "",
    phone: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
  };

  const contactInfo = [
    {
      icon: "✉️",
      title: t("contact.info.email.title"),
      details: t("contact.info.email.details"),
      description: t("contact.info.email.description"),
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600",
    },
    {
      icon: "📞",
      title: t("contact.info.phone.title"),
      details: t("contact.info.phone.details"),
      description: t("contact.info.phone.description"),
      bgColor: "bg-green-50",
      iconColor: "text-green-600",
    },
    {
      icon: "📍",
      title: t("contact.info.office.title"),
      details: t("contact.info.office.details"),
      description: t("contact.info.office.description"),
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600",
    },
    {
      icon: "💬",
      title: t("contact.info.chat.title"),
      details: t("contact.info.chat.details"),
      description: t("contact.info.chat.description"),
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600",
    },
  ];

  return (
    <div
      className={`bg-gradient-to-br from-slate-50 to-white min-h-screen ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Hero Section */}
      <div className="bg-[#e9f5ec] text-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`max-w-4xl mx-auto ${isRTL ? "text-right" : "text-center"}`}
          >
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-primary">
              {t("contact.hero.title")}
            </h1>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              {t("contact.hero.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Contact Cards */}
      <div className="py-16 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#1d4930]/20"
              >
                <div
                  className={`w-12 h-12 ${info.bgColor} rounded-lg flex items-center justify-center mb-4`}
                >
                  <span className={`text-xl ${info.iconColor}`}>
                    {info.icon}
                  </span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {info.title}
                </h3>
                <p className="text-[#1d4930] font-medium mb-1">
                  {info.details}
                </p>
                <p className="text-sm text-gray-600">{info.description}</p>
              </div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 bg-[#1d4930] rounded-lg flex items-center justify-center mr-4">
                  <span className="text-white text-xl">📝</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {t("contact.form.title")}
                  </h2>
                  <p className="text-gray-600">{t("contact.form.subtitle")}</p>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="group">
                    <label
                      htmlFor="name"
                      className="block text-sm font-semibold text-gray-700 mb-2"
                    >
                      {t("contact.form.name")} *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] bg-gray-50 focus:bg-white transition-all duration-200"
                      placeholder={t("contact.form.placeholder.name")}
                    />
                  </div>

                  <div className="group">
                    <label
                      htmlFor="email"
                      className="block text-sm font-semibold text-gray-700 mb-2"
                    >
                      {t("contact.form.email")} *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] bg-gray-50 focus:bg-white transition-all duration-200"
                      placeholder={t("contact.form.placeholder.email")}
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="group">
                    <label
                      htmlFor="company"
                      className="block text-sm font-semibold text-gray-700 mb-2"
                    >
                      {t("contact.form.company")}
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] bg-gray-50 focus:bg-white transition-all duration-200"
                      placeholder={t("contact.form.placeholder.company")}
                    />
                  </div>

                  <div className="group">
                    <label
                      htmlFor="phone"
                      className="block text-sm font-semibold text-gray-700 mb-2"
                    >
                      {t("contact.form.phone")}
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] bg-gray-50 focus:bg-white transition-all duration-200"
                      placeholder={t("contact.form.placeholder.phone")}
                    />
                  </div>
                </div>

                <div className="group">
                  <label
                    htmlFor="subject"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    {t("contact.form.subject")} *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] bg-gray-50 focus:bg-white transition-all duration-200"
                  >
                    <option value="">
                      {t("contact.form.placeholder.subject")}
                    </option>
                    <option value="general">
                      {t("contact.form.subjects.general")}
                    </option>
                    <option value="tax-advisory">
                      {t("contact.form.subjects.tax")}
                    </option>
                    <option value="company-formation">
                      {t("contact.form.subjects.company")}
                    </option>
                    <option value="trust-formation">
                      {t("contact.form.subjects.trust")}
                    </option>
                    <option value="consultation">
                      {t("contact.form.subjects.consultation")}
                    </option>
                  </select>
                </div>

                <div className="group">
                  <label
                    htmlFor="message"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    {t("contact.form.message")} *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-[#1d4930] resize-vertical bg-gray-50 focus:bg-white transition-all duration-200"
                    placeholder={t("contact.form.placeholder.message")}
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-[#1d4930] to-[#2d5940] text-white py-4 px-8 rounded-xl hover:from-[#2d5940] hover:to-[#1d4930] transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {t("contact.form.send")}
                  <span className={`${isRTL ? "mr-2" : "ml-2"}`}>→</span>
                </button>
              </form>
            </div>

            {/* Additional Information */}
            <div className="space-y-8">
              {/* Why Choose Us */}
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div
                  className={`flex items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <div
                    className={`w-12 h-12 bg-gradient-to-r from-[#1d4930] to-[#2d5940] rounded-lg flex items-center justify-center ${isRTL ? "ml-4" : "mr-4"}`}
                  >
                    <span className="text-white text-xl">⭐</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    {t("contact.whyChoose.title")}
                  </h3>
                </div>
                <div className="space-y-4">
                  <div
                    className={`flex items-start ${isRTL ? "space-x-reverse flex-row-reverse" : ""} space-x-3`}
                  >
                    <div className="w-2 h-2 bg-[#1d4930] rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      {t("contact.whyChoose.expert")}
                    </p>
                  </div>
                  <div
                    className={`flex items-start ${isRTL ? "space-x-reverse flex-row-reverse" : ""} space-x-3`}
                  >
                    <div className="w-2 h-2 bg-[#1d4930] rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      {t("contact.whyChoose.personalized")}
                    </p>
                  </div>
                  <div
                    className={`flex items-start ${isRTL ? "space-x-reverse flex-row-reverse" : ""} space-x-3`}
                  >
                    <div className="w-2 h-2 bg-[#1d4930] rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      {t("contact.whyChoose.support")}
                    </p>
                  </div>
                </div>
              </div>

              {/* Business Hours */}
              <div className="bg-gradient-to-br from-[#1d4930]/5 to-[#2d5940]/5 p-8 rounded-2xl border border-[#1d4930]/10">
                <div
                  className={`flex items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <div
                    className={`w-12 h-12 bg-[#1d4930] rounded-lg flex items-center justify-center ${isRTL ? "ml-4" : "mr-4"}`}
                  >
                    <span className="text-white text-xl">🕒</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">
                    {t("contact.hours.title")}
                  </h3>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="font-medium text-gray-700">
                      {t("contact.hours.weekdays")}
                    </span>
                    <span className="text-[#1d4930] font-semibold">
                      {t("contact.hours.weekdaysTime")}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="font-medium text-gray-700">
                      {t("contact.hours.friday")}
                    </span>
                    <span className="text-[#1d4930] font-semibold">
                      {t("contact.hours.fridayTime")}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="font-medium text-gray-700">
                      {t("contact.hours.saturday")}
                    </span>
                    <span className="text-red-500 font-semibold">
                      {t("contact.hours.saturdayTime")}
                    </span>
                  </div>
                </div>
              </div>

              {/* Emergency Contact */}
              {/* <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-2xl border border-orange-200">
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">🚨</span>
                  <h4 className="font-bold text-gray-900">Emergency Contact</h4>
                </div>
                <p className="text-gray-700 text-sm mb-2">
                  For urgent matters outside business hours:
                </p>
                <p className="text-[#1d4930] font-bold">+971 50 123 4567</p>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactScreen;
